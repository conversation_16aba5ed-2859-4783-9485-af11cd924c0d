<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="key => (treeTabKey = key)">
        <a-tab-pane key="1" tab="站点类别">
          <TreeGeneral
            v-if="treeTabKey === '1'"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="treeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'category')"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="行政区划">
          <TreeGeneral
            v-if="treeTabKey === '2'"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="districtTreeOptions"
            @onTreeMounted="onTreeMounted"
            @select="node => clickTreeNode(node, 'district')"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="站点编码">
          <a-input
            v-model="queryParam.siteCode"
            placeholder="请输入站点编码"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>
        <a-form-item label="站点名称">
          <a-input
            v-model="queryParam.siteName"
            placeholder="请输入站点名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="所属工程">
          <a-tree-select
            v-model="queryParam.projectId"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            :tree-data="projectOptions"
            show-search
            treeNodeFilterProp="title"
            allowClear
            placeholder="请选择所属工程"
            :replaceFields="{
              children: 'children',
              title: 'projectName',
              key: 'projectId',
              value: 'projectId',
            }"
            tree-default-expand-all
          ></a-tree-select>
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" icon="download" :loading="exportLoading" @click="handleExport">导出</a-button>
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      ref="formDrawerRef"
      :districtOptions="districtTreeOptions.dataSource"
      :projectOptions="projectOptions"
      :siteOptions="treeOptions.dataSource"
      :riverSystemOptions="riverSystemOptions"
      :deptOptions="deptOptions"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getTreeByCode, getSitePage, deleteSite } from './services'
  import { getDistrictTree, getProjectTree, getRiverSystemList, getConfigTree } from '@/api/common'
  import FormDrawer from './modules/FormDrawer'
  import TreeGeneral from '@/components/TreeGeneral'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import excelExport from '@/utils/excelExport.js'
  import moment from 'moment'

  export default {
    name: 'SiteManage',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        treeTabKey: '1',
        exportLoading: false,
        treeOptions: {
          getDataApi: getTreeByCode,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'objectCategoryName',
            key: 'objectCategoryId',
          },
        },
        districtTreeOptions: {
          getDataApi: getDistrictTree,
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'districtName',
            key: 'districtCode',
          },
        },
        showFormDrawer: false,
        projectOptions: [],
        riverSystemOptions: [],
        deptOptions: [],

        list: [],
        tableTitle: '',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          siteCode: undefined,
          siteName: undefined,
          objectCategoryId: undefined,
          districtCode: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        districtTypes: {},
        siteTypes: {},
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '站点编码',
            field: 'siteCode',
            minWidth: 120,
          },
          {
            title: '站点名称',
            field: 'siteName',
            minWidth: 160,
            showOverflow: 'tooltip',
          },
          {
            title: '行政区划',
            field: 'districtCode',
            minWidth: 100,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.districtTypes[row.districtCode]?.districtName || ''
              },
            },
          },
          {
            title: '站点类别',
            field: 'objectCategoryId',
            minWidth: 140,
            showOverflow: 'tooltip',
            slots: {
              default: ({ row, rowIndex }) => {
                return this.siteTypes[row.objectCategoryId]?.objectCategoryName || ''
              },
            },
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 140,
            showOverflow: 'tooltip',
          },
          {
            title: '备注',
            field: 'remark',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      this.init()
    },
    computed: {},
    watch: {},
    methods: {
      init() {
        getDistrictTree().then(res => {
          this.districtTreeOptions.dataSource = res.data
          this.districtTypes = getFlatTreeMap(res.data, 'districtCode')
        })

        getRiverSystemList().then(res => {
          this.riverSystemOptions = res.data.map(e => {
            return {
              value: e.riverSystemId,
              label: e.riverSystemName,
            }
          })
        })
        getConfigTree({
          deptName: null,
          type: '1',
        }).then(res => {
          this.deptOptions = res.data[0].children.map(el => {
            return {
              value: el.deptId,
              label: el.deptName,
            }
          })
        })
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        // 根据树默认选中id来判断
        if (this.queryParam.objectCategoryId || this.queryParam.districtCode) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getSitePage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          siteCode: undefined,
          siteName: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.siteId)
        this.names = valObj.records.map(item => item.siteName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        if (this.treeTabKey === '1') {
          this.queryParam.objectCategoryId = data[0].objectCategoryId
          this.queryParam.districtCode = ''
          this.tableTitle = data[0].objectCategoryName

          this.siteTypes = getFlatTreeMap(data, 'objectCategoryId')
          this.treeOptions.dataSource = data
        }
        if (this.treeTabKey === '2') {
          this.queryParam.objectCategoryId = ''
          this.queryParam.districtCode = data[0].districtCode
          this.tableTitle = data[0].districtName

          // this.districtTreeOptions.dataSource = data
          // this.districtTypes = getFlatTreeMap(data, 'districtCode')
        }

        this.getList()
        // 获取工程树
        getProjectTree({ objectCategoryId: null }).then(res => {
          this.projectOptions = res.data
        })
      },

      clickTreeNode(node, type) {
        if (type === 'category') {
          this.queryParam.objectCategoryId = node.$options.propsData.eventKey
        }
        if (type === 'district') {
          this.queryParam.districtCode = node.$options.propsData.eventKey
        }

        this.tableTitle = node.$options.propsData.dataRef.title
        this.queryParam.pageNum = 1
        this.getList()
      },

      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        if (this.treeTabKey === '1') {
          this.$nextTick(() =>
            this.$refs.formDrawerRef.handleAdd({ objectCategoryId: this.queryParam.objectCategoryId }, '1'),
          )
        }
        if (this.treeTabKey === '2') {
          this.$nextTick(() => this.$refs.formDrawerRef.handleAdd({ districtCode: this.queryParam.districtCode }, '2'))
        }
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const siteIds = row.siteId ? [row.siteId] : this.ids
        const names = row.siteName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteSite({ siteIds: siteIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },

      // 导出
      handleExport() {
        this.exportLoading = true
        getSitePage({ ...this.queryParam, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
          this.exportLoading = false

          const exportColumns = [
            {
              title: '站点编码',
              field: 'siteCode',
              minWidth: 120,
            },
            {
              title: '站点名称',
              field: 'siteName',
              minWidth: 160,
            },
            {
              title: '站点简称',
              field: 'siteNameAbbr',
              minWidth: 120,
            },
            {
              title: '行政区划',
              field: 'districtCode',
              minWidth: 100,
            },
            {
              title: '站点类别',
              field: 'objectCategoryId',
              minWidth: 140,
            },
            {
              title: '所属工程',
              field: 'projectName',
              minWidth: 140,
            },
            {
              title: '所属水系',
              field: 'riverSystemId',
              minWidth: 140,
            },
            {
              title: '经度',
              field: 'longitude',
              minWidth: 140,
            },
            {
              title: '纬度',
              field: 'latitude',
              minWidth: 140,
            },
            {
              title: '备注',
              field: 'remark',
              minWidth: 100,
            },
          ]

          const data = (res.data?.data || []).map(row => ({
            ...row,
            districtCode: this.districtTypes[row.districtCode]?.districtName || '',
            objectCategoryId: this.siteTypes[row.objectCategoryId]?.objectCategoryName || '',
            riverSystemId: this.riverSystemOptions.find(e => e.value === row.riverSystemId)?.label || '',
          }))

          excelExport(exportColumns, data, `${this.$route.meta.title}${moment().format('YYYYMMDDHHmmss')}`)
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
