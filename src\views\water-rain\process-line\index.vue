<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <a-tabs v-model="treeTabKey" @change="onTabChange">
        <a-tab-pane key="1" tab="按站点">
          <SingleTree
            v-if="treeTabKey === '1' && !!categoryTreeOptions.dataSource.length"
            :key="1"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="categoryTreeOptions"
            :currentKeys="currentKeys"
            @onTreeMounted="onTreeMounted"
            @select="(keys, item) => clickTreeNode(keys, item)"
          />
        </a-tab-pane>

        <a-tab-pane key="2" tab="按区域">
          <SingleTree
            v-if="treeTabKey === '2' && !!districtTreeOptions.dataSource.length"
            :key="2"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="districtTreeOptions"
            :currentKeys="currentKeys"
            @onTreeMounted="onTreeMounted"
            @select="(keys, item) => clickTreeNode(keys, item)"
          />
        </a-tab-pane>

        <a-tab-pane key="3" tab="按单位">
          <SingleTree
            v-if="treeTabKey === '3' && !!orgTreeOptions.dataSource.length"
            :key="3"
            hasTab
            style="width: 220px"
            ref="treeGeneralRef"
            :treeOptions="orgTreeOptions"
            :currentKeys="currentKeys"
            @onTreeMounted="onTreeMounted"
            @select="(keys, item) => clickTreeNode(keys, item)"
          />

          <!-- <TreeOrg
            v-if="treeTabKey === '3' && !!orgTreeOptions.dataSource.length"
            style="min-width: 130px; max-width: 160px; width: 220px"
            ref="treeOrgRef"
            :treeOptions="orgTreeOptions"
            @select="(node, item) => clickTreeNodeOrg(node, item)"
            @onTreeMountedOrg="onTreeMountedOrg"
          /> -->
        </a-tab-pane>
      </a-tabs>
    </div>

    <div class="tree-table-right-panel">
      <RainWaterLine v-if="site && site.key" :site="site" />
    </div>
  </div>
</template>

<script lang="jsx">
  import { getCategoryTree, getRiverSiteTree, getDistrictSiteTree, getRealTimeList } from './services'
  import { getDeptSiteTree } from '@/api/common'
  import SingleTree from '@/components/TreeGeneral/singleTree.vue'
  import RainWaterLine from './modules/RainWaterLine/index.vue'
  import TreeOrg from '@/components/TreeOrg'

  export default {
    name: 'ProcessLine',
    components: {
      SingleTree,
      RainWaterLine,
      TreeOrg,
    },
    data() {
      return {
        routeCode: null,
        routeType: 1,
        treeTabKey: '1',
        orgTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'id',
          },
        },
        categoryTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'id',
          },
        },
        districtTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'id',
          },
        },
        riverSiteTreeOptions: {
          dataSource: [],
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'id',
          },
        },
        currentKeys: [],
        site: null,
      }
    },
    created() {
      const url = window.location.href

      if (url.includes('comprehensive-monitoring/multi-process-line')) {
        this.routeType = 2
      } else if (url.includes('water-rain/process-line')) {
        this.routeType = 1
      }
      let code = this.$route.meta.query?.['code']
      //1'MS001_SS' 2'MS001_EL' 3'MS001_SLJL' 4'MS001_ZQ' 5'MS001_ZZ' 6'MS001_SL'
      this.routeCode =
        code == 1
          ? 'MS001_SS'
          : code == 2
            ? 'MS001_EL'
            : code == 3
              ? 'MS001_SLJL'
              : code == 4
                ? 'MS001_ZQ'
                : code == 5
                  ? 'MS001_ZZ'
                  : code == 6
                    ? 'MS001_SL'
                    : code == 7
                      ? 'MS001_FL'
                      : undefined
      let param = { labels: this.routeType, objectCategoryCode: this.routeCode }
      let newTree = []

      getCategoryTree(param).then(res => {
        let list = res.data[0].children[0].children
        if (this.routeCode) {
          newTree = list.filter(item => item.code == this.routeCode) //'MS001_SS'
        } else {
          // this.categoryTreeOptions.dataSource = res.data
          newTree = res.data
        }
        setTimeout(() => {
          this.$nextTick(() => {
            this.categoryTreeOptions.dataSource = newTree
          })
        }, 200)
      })
      getDistrictSiteTree(param).then(res => {
        this.districtTreeOptions.dataSource = res.data
      })
      getRiverSiteTree(param).then(res => {
        this.riverSiteTreeOptions.dataSource = res.data
      })

      getDeptSiteTree(param).then(res => {
        this.orgTreeOptions.dataSource = res.data
      })
    },
    computed: {},
    watch: {},
    methods: {
      // 树加载完成后
      onTreeMounted(selectedKeys, item) {
        this.currentKeys = selectedKeys
        this.site = { ...item, key: item.key.slice(1, item.key.length) }
      },
      clickTreeNode(selectedKeys, item) {
        this.currentKeys = selectedKeys
        this.site = { ...item, key: item.key.slice(1, item.key.length) }
      },
      //点击机构树数据
      clickTreeNodeOrg(node, item) {
        this.currentKeys = node.$options.propsData.eventKey
        this.site = { ...item, key: item.key.slice(1, item.key.length) }
      },
      // 机构树加载完成后
      onTreeMountedOrg(data) {
        if (this.treeOrgTabKey == '0') {
          this.currentKeys = data[0].deptId
        }
      },

      onTabChange(key) {
        this.treeTabKey = key
      },
    },
  }
</script>
<style lang="less" scoped>
  .tree-table-tree-panel {
    ::v-deep .ant-tabs-card .ant-tabs-card-bar {
      // border: none;
      margin-bottom: 5px;
      .ant-tabs-nav-container {
        height: auto;
        .ant-tabs-tab {
          height: 30px;
          line-height: 30px;
          margin-right: 5px;
          margin-top: 0px;
        }
      }
    }
  }
</style>
