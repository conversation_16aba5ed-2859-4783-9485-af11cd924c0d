<template>
  <div class="tree-table-page">
    <!-- 左侧树 -->
    <div class="tree-table-tree-panel">
      <TreeGeneral
        style="width: 220px"
        ref="treeGeneralRef"
        :treeOptions="treeOptions"
        @onTreeMounted="onTreeMounted"
        @select="clickTreeNode"
      />
    </div>

    <!-- 筛选栏 -->
    <div class="tree-table-right-panel">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="视频点名称">
          <a-input
            v-model="queryParam.cameraName"
            placeholder="请输入视频点名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          />
        </a-form-item>

        <a-form-item label="视频类型">
          <a-select allow-clear placeholder="请输入" v-model="queryParam.cameraType" option-filter-prop="children">
            <a-select-option :value="1">枪机</a-select-option>
            <a-select-option :value="2">球机</a-select-option>
          </a-select>
          <!-- <a-input
            v-model="queryParam.objectCategoryName"
            placeholder="请输入类别名称"
            allow-clear
            @keyup.enter.native="handleQuery"
          /> -->
        </a-form-item>

        <template #table>
          <VxeTable
            ref="vxeTableRef"
            :tableTitle="tableTitle"
            :columns="columns"
            :tableData="list"
            :loading="loading"
            :isAdaptPageSize="true"
            @adaptPageSizeChange="adaptPageSizeChange"
            @refresh="getList"
            @selectChange="selectChange"
            @sortChange="sortChange"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          >
            <div class="table-operations" slot="button">
              <a-button type="primary" @click="handleAdd()">
                <a-icon type="plus" />
                新增
              </a-button>
              <a-button type="danger" v-if="isChecked" @click="handleDelete">
                <a-icon type="delete" />
                删除
              </a-button>
            </div>
          </VxeTable>
        </template>
      </VxeTableForm>
    </div>

    <FormDrawer
      v-if="showFormDrawer"
      ref="formDrawerRef"
      :districtOptions="districtOptions"
      :projectOptions="projectOptions"
      :riverSystemOptions="riverSystemOptions"
      :otherObjectOptions="otherObjectOptions"
      :siteOptions="siteOptions"
      :deptOptions="deptOptions"
      @ok="onOperationComplete"
      @close="showFormDrawer = false"
    />
  </div>
</template>

<script lang="jsx">
  import { getCameraPage, deleteCamera, getBaseSite } from './services'
  import { getDistrictTree, getProjectTree, getProjectCategoryTree, getConfigTree } from '@/api/common'
  import { getBaseOtherObject } from '@/views/basic/site-manage/services'
  import { getRiverSystemList } from '@/api/common'

  import FormDrawer from './modules/FormDrawer'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'

  export default {
    name: 'Camera',
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      FormDrawer,
    },
    data() {
      return {
        treeOptions: {
          getDataApi: getProjectCategoryTree,
          replaceFields: {
            children: 'children',
            title: 'name',
            key: 'id',
            value: 'key',
          },
        },
        showFormDrawer: false,
        districtOptions: [],
        projectOptions: [], //工程树
        riverSystemOptions: [], //水系树
        otherObjectOptions: [], //其他对象树
        siteOptions: [], //站点树
        deptOptions: [], //部门树

        list: [],
        tableTitle: '视频监控',
        isChecked: false,
        ids: [],
        names: [],
        loading: false,
        total: 0,

        queryParam: {
          treeNodeId: undefined,
          treeNodeType: undefined,
          cameraType: undefined,
          cameraName: undefined,
          pageNum: 1,
          pageSize: 10,
          sort: [],
        },
        districtTypes: [],
        columns: [
          { type: 'checkbox', width: 30 },
          { type: 'seq', title: '序号', width: 50 },
          {
            title: '视频点编号',
            field: 'cameraCode',
            minWidth: 140,
          },
          {
            title: '视频点名称',
            field: 'cameraName',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '设备号',
            field: 'deviceCode',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '通道号',
            field: 'channelCode',
            minWidth: 180,
            showOverflow: 'tooltip',
          },
          {
            title: '类型',
            field: 'cameraType',
            minWidth: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                // 摄像头类型, 1-枪机 2-球机
                return row.cameraType ? (row.cameraType == 1 ? '枪机' : '球机') : ''
              },
            },
          },
          {
            title: '所属工程',
            field: 'projectName',
            minWidth: 100,
            showOverflow: 'tooltip',
          },
          {
            title: '操作',
            field: 'operate',
            width: 100,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.handleUpdate(row)}>修改</a>
                    <a-divider type='vertical' />
                    <a onClick={() => this.handleDelete(row)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],
      }
    },
    created() {
      // 行政区划扁平枚举
      getDistrictTree().then(res => {
        this.districtOptions = res.data
        let temArr = []
        function getTreeTypes(arr) {
          arr?.forEach(el => {
            temArr.push(el)
            getTreeTypes(el.children)
          })
        }
        getTreeTypes(res.data)
        this.districtTypes = temArr
      })
      this.init()
    },
    computed: {},
    watch: {},
    methods: {
      init() {
        getBaseSite().then(res => {
          this.siteOptions = res.data.map(e => {
            return {
              value: e.siteId,
              label: e.siteName,
            }
          })
        })
        // getRiverSystemTree().then(res => {
        //   this.riverSystemOptions = res.data
        // })
        getRiverSystemList().then(res => {
          this.riverSystemOptions = res.data.map(e => {
            return {
              value: e.riverSystemId,
              label: e.riverSystemName,
            }
          })
        })
        getBaseOtherObject().then(res => {
          this.otherObjectOptions = res.data.map(e => {
            return {
              value: e.otherObjectId,
              label: e.otherObjectName,
            }
          })
        })
        getConfigTree({
          deptName: null,
          type: '1',
        }).then(res => {
          this.deptOptions = res.data[0].children.map(el => {
            return {
              value: el.deptId,
              label: el.deptName,
            }
          })
        })
      },
      adaptPageSizeChange(pageSize) {
        this.queryParam.pageSize = pageSize
        // 根据树默认选中id来判断
        if (this.queryParam.treeNodeId) {
          this.getList()
        }
      },
      /** 查询列表 */
      getList() {
        this.showFormDrawer = false
        this.loading = true
        this.selectChange({ records: [] })
        getCameraPage(this.queryParam).then(response => {
          this.list = response.data.data
          this.total = response.data.total
          this.loading = false
        })
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.queryParam = {
          ...this.queryParam,
          cameraName: undefined,
          cameraType: undefined,

          pageNum: 1,
          pageSize: 10,
          sort: [],
        }
        this.handleQuery()
      },

      // 操作完成后
      onOperationComplete() {
        this.getList()
        // this.$refs.treeGeneralRef.getDataSource()
      },

      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      // 多选框选中
      selectChange(valObj) {
        this.ids = valObj.records.map(item => item.cameraId)
        this.names = valObj.records.map(item => item.cameraName)
        this.isChecked = !!valObj.records.length
      },
      // 排序
      sortChange(valObj) {
        this.queryParam.sort = valObj.sortList.map(item => ({ property: item.field, direction: item.order }))
        this.getList()
      },

      // 树加载完成后
      onTreeMounted(data) {
        this.queryParam.treeNodeId = data[0].key.substr(1, data[0].key.length)
        this.queryParam.treeNodeType = data[0].type

        this.getList()

        // 获取工程树
        getProjectTree({ objectCategoryId: this.queryParam.treeNodeId }).then(res => {
          this.projectOptions = res.data
        })
      },
      clickTreeNode(node) {
        const key = node.$options.propsData.dataRef.key
        this.queryParam.treeNodeId = key.substr(1, key.length)
        this.queryParam.treeNodeType = node.$options.propsData.dataRef.type

        this.queryParam.pageNum = 1
        this.getList()

        // // 获取工程树
        // getProjectTree({ objectCategoryId: null }).then(res => {
        //   this.projectOptions = res.data
        // })
      },

      /* 新增 */
      handleAdd() {
        this.showFormDrawer = true
        this.$nextTick(() =>
          this.$refs.formDrawerRef.handleAdd({
            treeNodeId: this.queryParam.treeNodeType === 'category' ? undefined : this.queryParam.treeNodeId,
          }),
        )
      },
      /* 修改 */
      handleUpdate(record) {
        this.showFormDrawer = true
        this.$nextTick(() => this.$refs.formDrawerRef.handleUpdate(record))
      },

      /** 删除按钮操作 */
      handleDelete(row) {
        var that = this
        const cameraIds = row.cameraId ? [row.cameraId] : this.ids
        const names = row.cameraName || this.names

        this.$confirm({
          title: '确认删除所选中数据?',
          content: '当前选中名称为"' + names + '"的数据',
          onOk() {
            return deleteCamera({ cameraIds: cameraIds.join(',') }).then(res => {
              that.$message.success(`成功删除 ${res.data} 条数据`, 3)
              that.selectChange({ records: [] })
              that.onOperationComplete()
            })
          },
          onCancel() {},
        })
      },

      onClickRow(record, index) {
        this.handleUpdate(record, '')
      },

      handleTableChange(pagination, filters, sorter) {
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  .ant-avatar {
    font-size: 12px;
    border-radius: 4px;
    vertical-align: middle;
    margin-right: 8px;
  }
</style>
