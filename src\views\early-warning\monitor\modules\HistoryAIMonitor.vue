<template>
  <ant-modal
    :visible="open"
    :modal-title="formTitle"
    :loading="modalLoading"
    modalWidth="1300"
    @cancel="cancel"
    modalHeight="800"
    :footer="null"
  >
    <div slot="content">
      <VxeTableForm @handleQuery="handleQuery" @resetQuery="resetQuery">
        <a-form-item label="预警编号">
          <a-input v-model="queryParam.eventNo" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery" />
        </a-form-item>
        <a-form-item label="预警时间">
          <a-range-picker
            allow-clear
            :value="takeEffect"
            format="YYYY-MM-DD"
            formatValue="YYYY-MM-DD"
            :placeholder="['开始时间', '结束时间']"
            @change="onRangeChange"
          />
        </a-form-item>
        <template #table>
          <VxeTable
            ref="vxeTableRef"
            tableTitle="历史预警"
            :columns="columns"
            :tableData="historyList"
            :loading="loading"
            @refresh="getHistoryList"
            :rowConfig="{ isCurrent: true, isHover: true }"
            :tablePage="{ pageNum: queryParam.pageNum, pageSize: queryParam.pageSize, total }"
            @handlePageChange="handlePageChange"
          ></VxeTable>
        </template>
      </VxeTableForm>
    </div>
    <template slot="footer">
      <a-button @click="cancel">取消</a-button>
    </template>
  </ant-modal>
</template>
<script lang="jsx">
  import { getAIEventHistoryList } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import moment from 'moment'

  export default {
    name: 'FormMaintenance',
    components: { AntModal, VxeTable, VxeTableForm },
    data() {
      return {
        modalLoading: false,
        formTitle: '查看历史预警',
        open: false,
        takeEffect: [],
        queryParam: {
          eventNo: undefined,
          objectId: null,
          pageNum: 1,
          pageSize: 10,
          sort: [],
          status: null,
          warnTimeLower: '',
          warnTimeUpper: '',
        },
        total: 0,
        historyList: [],
        loading: false,
        columns: [
          {
            type: 'seq',
            title: '序号',
            minWidth: 50,
            slots: {
              default: ({ rowIndex }) => {
                return rowIndex + 1 + (this.queryParam.pageNum - 1) * this.queryParam.pageSize
              },
            },
          },
          {
            title: '预警编号',
            field: 'eventNo',
            minWidth: 120,
            showOverflow: 'tooltip',
          },

          // {
          //   title: '对象类别',
          //   field: 'objectCategoryName',
          // },
          {
            title: '预警描述',
            field: 'message',
            minWidth: 600,
            showOverflow: 'tooltip',
          },
          {
            title: '当前状态',
            field: 'status',
            minWidth: 80,
            slots: {
              default: ({ row }) => {
                if (row.status == 1) {
                  return <span>正在报警</span>
                } else if (row.status == 2) {
                  return <span>已消除</span>
                } else if (row.status == 3) {
                  return <span>已失效</span>
                }
              },
            },
          },
          {
            title: '预警时间',
            field: 'createdTime',
            minWidth: 120,
          },
          {
            title: '附件',
            field: 'url',
            minWidth: 50,
            slots: {
              default: ({ row }) => {
                return row.url ? (
                  <a href={row.url}>附件</a>
                ) : (
                  <a style={'color: #999'} href='javascript:;'>
                    无
                  </a>
                )

                // <div>
                //   <a-button
                //     type='primary'
                //     size='small'
                //     onClick={() => this.handleUrl(row)}
                //   >
                //     查看
                //   </a-button>

                // </div>
              },
            },
          },
        ],
      }
    },
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 新增按钮操作 */
      historyMonitor(row) {
        this.open = true
        this.queryParam.objectId = 1
        this.queryParam.objectIds = row.objectIds

        this.modalLoading = true
        this.getHistoryList()
      },
      getHistoryList() {
        getAIEventHistoryList(this.queryParam).then(res => {
          this.modalLoading = false
          this.historyList = res?.data?.data || []
          this.total = res?.data?.total
        })
      },
      onRangeChange(value, dateString) {
        this.takeEffect = value
        if (dateString.length == 0) {
          return
        }
        this.queryParam.warnTimeLower = dateString[0] ? moment(dateString[0]).format('YYYY-MM-DD') : null
        this.queryParam.warnTimeUpper = dateString[1] ? moment(dateString[1]).format('YYYY-MM-DD') : null
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParam.pageNum = 1
        this.getHistoryList()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.takeEffect = []
        this.queryParam.eventNo = undefined
        this.queryParam.warnTimeLower = null
        this.queryParam.warnTimeUpper = null
        this.queryParam.pageNum = 1
        this.queryParam.pageSize = 10
        this.handleQuery()
      },
      // 分页
      handlePageChange({ currentPage, pageSize }) {
        this.queryParam.pageNum = currentPage
        this.queryParam.pageSize = pageSize
        this.getList()
      },
    },
  }
</script>
<style lang="less" scoped>
  ::v-deep .ant-modal-body {
    overflow: hidden !important;
  }
  ::v-deep .title {
    font-size: 15px;
    color: #1890ff !important;
  }
  ::v-deep .vxe-table-box,
  ::v-deep .vxe-table-content .vxe-table-box .vxe-table-box-content {
    height: 500px !important;
  }
</style>
