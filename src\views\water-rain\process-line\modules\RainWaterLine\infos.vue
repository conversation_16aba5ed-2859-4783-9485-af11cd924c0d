<template>
  <div class="infos">
    <!--  -->
    <!-- <span class="item" v-if="waterQualityName">
      <span class="label">当前水质等级:</span>
      <span class="value">{{ waterQualityName }}</span>
    </span> -->
    <span class="item" v-if="(indexCode == 'all' && getAllInfoShow('rainfall')) || indexCode == 'rainfall'">
      <span class="label">累计降雨量(mm):</span>
      <span class="value">{{ getFixedNum(dataSource.sumRain, 1) }}</span>
    </span>
    <span class="item" v-if="indexCode == 'all' && getAllInfoShow('waterLevel')">
      <span class="label">最高水位(m):</span>
      <span class="value">{{ getFixedNum(dataSource.maxWaterLevel, 2) }}</span>
      <span class="time">{{ dataSource.maxWaterLevelTime }}</span>
    </span>
    <span class="item" v-if="indexCode == 'all' && getAllInfoShow('flow')">
      <span class="label">最大流量(m³/s):</span>
      <span class="value">{{ dealNumber(dataSource.maxFlow, 3) }}</span>
      <span class="time">{{ dataSource.maxFlowTime }}</span>
    </span>

    <span class="item" v-if="indexCode == 'rainfall'">
      <span class="label">时段最高雨量(mm):</span>
      <span class="value">{{ getFixedNum(dataSource.maxRain, 1) }}</span>
      <span class="time">{{ dataSource.maxRainTime }}</span>
    </span>

    <span class="item" v-if="indexCode == 'waterLevel'">
      <span class="label">时段最高水位(m):</span>
      <span class="value">{{ getFixedNum(dataSource.maxWaterLevel, 2) }}</span>
      <span class="time">{{ dataSource.maxWaterLevelTime }}</span>
    </span>

    <span class="item" v-if="indexCode == 'flow'">
      <span class="label">时段最大流量(m³/s):</span>
      <span class="value">{{ dealNumber(dataSource.maxFlow, 3) }}</span>
      <span class="time">{{ dataSource.maxFlowTime }}</span>
    </span>
    <span class="item" v-if="indexCode == 'flow'">
      <span class="label">时段最低流量(m³/s):</span>
      <span class="value">{{ dealNumber(dataSource.minFlow, 3) }}</span>
      <span class="time">{{ dataSource.minFlowTime }}</span>
    </span>

    <span
      class="item"
      v-if="indexCode !== 'waterLevel' && indexCode !== 'rainfall' && indexCode !== 'flow' && indexCode !== 'all'"
    >
      <span class="label">最高{{ multiSourceOptions[source]?.label + multiSourceOptions[source]?.unit }}:</span>
      <span class="value">{{ dataSource.maxIndexValue }}</span>
      <span class="time">{{ dataSource.maxDateTime }}</span>
    </span>
    <span
      class="item"
      v-if="indexCode !== 'waterLevel' && indexCode !== 'rainfall' && indexCode !== 'flow' && indexCode !== 'all'"
    >
      <span class="label">最低{{ multiSourceOptions[source]?.label + multiSourceOptions[source]?.unit }}:</span>
      <span class="value">{{ dataSource.minIndexValue }}</span>
      <span class="time">{{ dataSource.minDateTime }}</span>
    </span>

    <!-- <span class="item" v-if="indexCode == 'ph'">
      <span class="label">最高PH:</span>
      <span class="value">{{ dataSource.maxIndexValue }}</span>
      <span class="time">{{ dataSource.maxDateTime }}</span>
    </span>
    <span class="item" v-if="indexCode == 'ph'">
      <span class="label">最低PH:</span>
      <span class="value">{{ dataSource.minIndexValue }}</span>
      <span class="time">{{ dataSource.minDateTime }}</span>
    </span>

    <span class="item" v-if="indexCode == 'temperature'">
      <span class="label">最高温度(℃):</span>
      <span class="value">{{ dataSource.maxIndexValue }}</span>
      <span class="time">{{ dataSource.maxDateTime }}</span>
    </span>
    <span class="item" v-if="indexCode == 'temperature'">
      <span class="label">最低温度(℃):</span>
      <span class="value">{{ dataSource.minIndexValue }}</span>
      <span class="time">{{ dataSource.minDateTime }}</span>
    </span>

    <span class="item" v-if="indexCode == 'electricConductivity'">
      <span class="label">最高电导率(μS/cm):</span>
      <span class="value">{{ dataSource.maxIndexValue }}</span>
      <span class="time">{{ dataSource.maxDateTime }}</span>
    </span>
    <span class="item" v-if="indexCode == 'electricConductivity'">
      <span class="label">最低电导率(μS/cm):</span>
      <span class="value">{{ dataSource.minIndexValue }}</span>
      <span class="time">{{ dataSource.minDateTime }}</span>
    </span>

    <span class="item" v-if="indexCode == 'dissolvedOxygen'">
      <span class="label">最高溶解氧(mg/L):</span>
      <span class="value">{{ dataSource.maxIndexValue }}</span>
      <span class="time">{{ dataSource.maxDateTime }}</span>
    </span>
    <span class="item" v-if="indexCode == 'dissolvedOxygen'">
      <span class="label">最低溶解氧(mg/L):</span>
      <span class="value">{{ dataSource.minIndexValue }}</span>
      <span class="time">{{ dataSource.minDateTime }}</span>
    </span> -->
  </div>
</template>

<script lang="jsx">
  import { dealNumber, getFixedNum } from '@/utils/dealNumber.js'
  // import { multiSourceOptions } from './config'

  export default {
    name: 'Infos',
    props: ['indexCode', 'dataSource', 'siteTerminalData', 'source', 'multiSourceOptions', 'waterQualityName'],
    data() {
      return { dealNumber, getFixedNum }
    },
    created() {},
    methods: {
      getAllInfoShow(indexCode) {
        return this.siteTerminalData.some(el => el.indexCode == indexCode)
      },
    },
  }
</script>

<style lang="less" scoped>
  .infos {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .item {
      .label {
        margin-right: 5px;
        color: #1d2129;
      }
      .value {
        margin-right: 10px;
        font-size: 17px;
        font-weight: 700;
        color: #1d2129;
      }
      .time {
        color: #757575;
      }
    }
  }
</style>
