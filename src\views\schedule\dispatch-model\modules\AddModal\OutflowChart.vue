<template>
  <div style="height: 100%; position: relative; display: flex; flex-direction: column;">
    <div style="height: 40px; padding: 8px 0; display: flex; justify-content: space-between; align-items: center; flex-shrink: 0;">
      <h3 style="font-weight: bold">出库流量趋势</h3>
      <div v-if="selectedSeries !== null && dispatchMethod !== 1">
        <span style="margin-right: 8px; font-size: 12px; color: #666;">
          已选中：{{ getSeriesName(selectedSeries) }}
        </span>
        <a-button
          v-if="!editMode"
          size="small"
          type="primary"
          @click="startEdit"
        >
          编辑
        </a-button>
        <a-button
          v-else
          size="small"
          type="primary"
          @click="finishEdit"
        >
          完成
        </a-button>
      </div>
    </div>
    
    <!-- 图表滚动容器 -->
    <div 
      ref="scrollContainer"
      style="flex: 1; overflow-x: auto; overflow-y: hidden; border: 1px solid #f0f0f0; border-radius: 4px;"
    >
      <div 
        ref="chartContainer" 
        style="height: 100%; min-width: 800px;"
      ></div>
    </div>
    
    <!-- 选中提示 -->
    <div
      v-if="selectedSeries !== null && !editMode && dispatchMethod !== 1"
      class="select-tip"
      style="position: absolute; top: 50px; left: 10px; background: rgba(24, 144, 255, 0.8); color: white; padding: 8px 12px; border-radius: 4px; font-size: 12px; z-index: 1000;"
    >
      已选中 {{ getSeriesName(selectedSeries) }}，点击编辑按钮开始编辑
    </div>
    
    <!-- 编辑模式提示 -->
    <!-- <div 
      v-if="editMode && selectedSeries !== null" 
      class="edit-tip"
      style="position: absolute; top: 50px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 8px 12px; border-radius: 4px; font-size: 12px; z-index: 1000;"
    >
      编辑模式：点击线条数据点弹框修改，或点击空白区域直接设置数值
    </div> -->
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'OutflowChart',
  props: {
    chartData: Array,
    dispatchMethod: Number, // 调度方式
  },
  data() {
    return {
      chart: null,
      editMode: false,
      selectedSeries: null, // 当前选中的系列索引
      currentMousePos: { x: 0, y: 0 }, // 当前鼠标位置
      localChartData: null, // 本地图表数据，用于编辑时保持小数精度
      isLocalUpdate: false, // 标记是否为本地更新，避免父组件覆盖
    }
  },
  mounted() {
    this.initChart()
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  watch: {
    chartData: {
      handler(newData) {
        // 如果是本地更新触发的，忽略父组件的数据变化
        if (this.isLocalUpdate) {
          this.isLocalUpdate = false
          return
        }

        // 初始化或非编辑模式时，同步父组件数据
        if (!this.editMode || !this.localChartData) {
          this.localChartData = JSON.parse(JSON.stringify(newData))
        }

        this.updateChart()
      },
      deep: true,
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
      
      // 监听图表点击事件 - 用于选择线条和编辑数值
      this.chart.on('click', (params) => {
        // 现状调度时禁用所有交互
        if (this.dispatchMethod === 1) {
          this.$message.warning('现状调度模式下数据不可编辑')
          return
        }

        if (params.componentType === 'series') {
          if (this.editMode && this.selectedSeries === params.seriesIndex) {
            this.handleDirectEdit(params)
          } else {
            this.handleSeriesClick(params)
          }
        }
      })

      // 监听图表区域点击事件（仅在编辑模式下用于修改数值）
      this.chart.getZr().on('click', (e) => {
        // 现状调度时禁用所有交互
        if (this.dispatchMethod === 1) {
          this.$message.warning('现状调度模式下数据不可编辑')
          return
        }

        // 使用延迟执行，让series事件先处理
        setTimeout(() => {
          // 编辑模式下，点击空白区域可直接设置数值
          if (this.editMode && this.selectedSeries !== null) {
            this.handleChartAreaClick(e)
          }
          // 非编辑模式，点击靠近线条的地方也可以选中线条
          // 但只有在没有直接点击到series时才执行
          else if (!this.editMode) {
            const foundSeries = this.findNearestLineSeries(e.offsetX, e.offsetY)
            if (foundSeries !== null) {
              // 检查该系列是否可以编辑
              const seriesName = this.getSeriesName(foundSeries)
              let canEdit = false
              if (this.dispatchMethod === 3) {
                // 手动调度：供水流量和泄洪流量都可编辑
                canEdit = (seriesName === '供水流量' || seriesName === '泄洪流量')
              } else if (this.dispatchMethod === 2) {
                // 推荐调度：只有供水流量可编辑
                canEdit = (seriesName === '供水流量')
              }

              if (canEdit) {
                // 检查是否刚刚通过series事件选中了同一个系列
                // 如果是，则不重复处理
                if (this.selectedSeries !== foundSeries) {
                  this.selectedSeries = foundSeries
                  this.updateChart()
                  this.$message.info(`已选中${seriesName}，点击编辑按钮开始编辑`)
                }
              } else {
                this.$message.warning(`${seriesName}在当前调度方式下不可编辑`)
              }
            }
          }
        }, 50) // 延迟50ms执行，确保series事件先处理完成
      })

      // 监听鼠标移动事件
      this.chart.getZr().on('mousemove', (e) => {
        this.currentMousePos = { x: e.offsetX, y: e.offsetY }

        // 现状调度时不显示编辑光标
        if (this.dispatchMethod === 1) {
          this.chart.getZr().dom.style.cursor = 'default'
          return
        }

        if (this.editMode && this.selectedSeries !== null) {
          this.updateCursor(e)
        }
      })
    },

    updateChart() {
      if (!this.chart) return

      // 使用本地数据或父组件数据
      const currentData = this.editMode && this.localChartData ? this.localChartData : this.chartData

      // 计算图表宽度，确保X轴不重叠
      const dataLength = currentData[0]?.data.length || 0
      const minWidth = Math.max(800, dataLength * 80) // 每个数据点80px宽度，增加间距

      // 设置容器宽度
      this.$refs.chartContainer.style.width = minWidth + 'px'
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: (params) => {
            let result = params[0].axisValue + '<br/>'
            params.forEach(param => {
              result += `${param.marker}${param.seriesName}: ${param.value}m³/s<br/>`
            })
            return result
          }
        },
        legend: {
          data: currentData.map(item => item.name),
          top: 10
        },
        grid: {
          left: 60,
          right: 60,
          bottom: 80,
          top: 50,
          containLabel: false
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: currentData[0]?.data.map(item => item[0]) || [],
          axisLabel: {
            formatter: function(value) {
              // 显示完整的日期时间，分两行显示
              const parts = value.split(' ')
              return parts[0] + '\n' + parts[1]
            },
            interval: 0, // 显示所有标签
            rotate: 0,
            fontSize: 10,
            margin: 15,
            lineHeight: 16
          },
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '流量(m³/s)',
          nameLocation: 'middle',
          nameGap: 40,
          nameTextStyle: {
            fontSize: 12
          }
        },
        series: currentData.map((item, index) => ({
          name: item.name,
          type: 'line',
          data: item.data.map(d => d[1]),
          smooth: false,
          symbol: 'circle',
          symbolSize: this.selectedSeries === index ? 10 : 8, // 增大数据点尺寸，提高可点击性
          lineStyle: {
            color: item.color,
            width: this.selectedSeries === index ? 4 : 2
          },
          itemStyle: {
            color: item.color,
            borderColor: this.selectedSeries === index ? '#fff' : item.color,
            borderWidth: this.selectedSeries === index ? 2 : 0
          },
          emphasis: {
            focus: 'series',
            lineStyle: {
              width: 4
            },
            itemStyle: {
              borderWidth: 2,
              borderColor: '#fff',
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          // 增加数据点的触发区域
          triggerLineEvent: true,
          // 确保数据点可以被点击
          silent: false
        }))
      }
      
      this.chart.setOption(option)
      
      // 触发图表重绘
      this.$nextTick(() => {
        this.chart.resize()
      })
    },

    // 处理窗口大小变化
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },

    // 获取系列名称
    getSeriesName(seriesIndex) {
      const currentData = this.editMode && this.localChartData ? this.localChartData : this.chartData
      return currentData[seriesIndex]?.name || ''
    },

    // 处理系列点击 - 用于选择线条
    handleSeriesClick(params) {
      const seriesName = params.seriesName
      const seriesIndex = params.seriesIndex
      
      // 判断是否可以编辑该系列
      let canEdit = false
      if (this.dispatchMethod === 3) {
        // 手动调度：供水流量和泄洪流量都可编辑
        canEdit = (seriesName === '供水流量' || seriesName === '泄洪流量')
      } else if (this.dispatchMethod === 2) {
        // 推荐调度：只有供水流量可编辑
        canEdit = (seriesName === '供水流量')
      }
      
      if (canEdit) {
        if (this.selectedSeries === seriesIndex && !this.editMode) {
          // 如果点击的是已选中的系列且不在编辑模式，取消选择
          this.selectedSeries = null
          this.editMode = false
          this.updateChart()
        } else {
          // 选择新的系列或重新选择
          this.selectedSeries = seriesIndex
          this.editMode = false // 重置编辑模式
          this.updateChart()
          this.$message.info(`已选中${seriesName}，点击编辑按钮开始编辑`)
        }
      } else {
        this.$message.warning(`${seriesName}在当前调度方式下不可编辑`)
      }
    },

    // 开始编辑
    startEdit() {
      if (this.selectedSeries !== null) {
        this.editMode = true
        // 初始化本地数据
        this.localChartData = JSON.parse(JSON.stringify(this.chartData))
        this.updateChart()
        this.$message.info(`开始编辑${this.getSeriesName(this.selectedSeries)}，点击线条可直接修改对应位置的数值`)
      }
    },

    // 直接编辑数值（点击线条上的点）- 通过点击位置直接设置数值
    handleDirectEdit(params) {
      // 获取当前鼠标位置对应的Y轴数值
      const pointInPixel = [this.currentMousePos.x, this.currentMousePos.y]
      const pointInGrid = this.chart.convertFromPixel({ seriesIndex: this.selectedSeries }, pointInPixel)

      if (pointInGrid && pointInGrid[1] !== undefined) {
        const dataIndex = params.dataIndex
        const clickedValue = Math.max(0, Math.round(pointInGrid[1] * 100) / 100) // 精确到两位小数，确保非负

        // 更新数据
        this.updateChartData(this.selectedSeries, dataIndex, clickedValue)
        this.$message.success(`已将${this.getSeriesName(this.selectedSeries)}第${dataIndex + 1}个数据点修改为${clickedValue}`)
      }
    },

    // 处理图表区域点击（Y轴方向调整）- 仅在编辑模式下生效
    handleChartAreaClick(e) {
      if (!this.editMode || this.selectedSeries === null) return

      const pointInPixel = [e.offsetX, e.offsetY]
      const pointInGrid = this.chart.convertFromPixel({ seriesIndex: this.selectedSeries }, pointInPixel)

      const currentData = this.editMode && this.localChartData ? this.localChartData : this.chartData
      if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < currentData[this.selectedSeries].data.length) {
        const dataIndex = Math.round(pointInGrid[0])
        const clickedValue = Math.max(0, Math.round(pointInGrid[1] * 100) / 100) // 精确到两位小数，确保非负

        // 更新数据
        this.updateChartData(this.selectedSeries, dataIndex, clickedValue)

        this.$message.success(`已将${this.getSeriesName(this.selectedSeries)}第${dataIndex + 1}个数据点修改为${clickedValue}`)
      }
    },

    // 更新鼠标样式
    updateCursor(e) {
      const pointInPixel = [e.offsetX, e.offsetY]
      const pointInGrid = this.chart.convertFromPixel({ seriesIndex: this.selectedSeries }, pointInPixel)
      const currentData = this.editMode && this.localChartData ? this.localChartData : this.chartData

      if (pointInGrid && pointInGrid[0] >= 0 && pointInGrid[0] < currentData[this.selectedSeries].data.length) {
        this.chart.getZr().dom.style.cursor = 'crosshair'
      } else {
        this.chart.getZr().dom.style.cursor = 'default'
      }
    },

    // 更新图表数据
    updateChartData(seriesIndex, dataIndex, newValue) {
      console.log('updateChartData called:', { seriesIndex, dataIndex, newValue })

      // 确保本地数据存在
      if (!this.localChartData) {
        this.localChartData = JSON.parse(JSON.stringify(this.chartData))
      }

      // 更新本地数据
      if (this.localChartData[seriesIndex] && this.localChartData[seriesIndex].data[dataIndex]) {
        const oldValue = this.localChartData[seriesIndex].data[dataIndex][1]
        console.log('Updating local value from', oldValue, 'to', newValue)

        this.localChartData[seriesIndex].data[dataIndex][1] = newValue

        console.log('Updated localChartData:', this.localChartData[seriesIndex].data[dataIndex])

        // 标记为本地更新，防止父组件数据覆盖
        this.isLocalUpdate = true

        // 强制更新图表
        this.$nextTick(() => {
          this.updateChart()
        })
      }

      // 发送数据变化事件给父组件，传递修改的系列索引、数据索引和新值
      this.$emit('dataChange', {
        seriesIndex,
        dataIndex,
        newValue
      })
    },

    // 完成编辑
    finishEdit() {
      this.editMode = false
      this.selectedSeries = null // 清除选择状态，按钮消失
      this.localChartData = null // 清除本地数据，回到使用父组件数据
      this.chart.getZr().dom.style.cursor = 'default'
      this.updateChart()
      this.$message.success('编辑完成')
      this.$emit('toggleEdit', false)
    },

    // 切换编辑模式（兼容旧版本）
    toggleEditMode() {
      if (this.editMode) {
        this.finishEdit()
      }
    },

    // 判断点击点是否靠近某条线段
    findNearestLineSeries(x, y) {
      const currentData = this.editMode && this.localChartData ? this.localChartData : this.chartData
      if (!this.chart || !currentData) return null
      const threshold = 8 // 距离阈值，像素
      let foundSeries = null
      let minDist = Infinity
      currentData.forEach((series, sIdx) => {
        const points = series.data.map((d, idx) => {
          return this.chart.convertToPixel({ seriesIndex: sIdx }, [idx, d[1]])
        })
        for (let i = 0; i < points.length - 1; i++) {
          const p1 = points[i]
          const p2 = points[i + 1]
          if (!p1 || !p2) continue
          const dist = this.pointToSegmentDistance(x, y, p1, p2)
          if (dist < threshold && dist < minDist) {
            foundSeries = sIdx
            minDist = dist
          }
        }
      })
      return foundSeries
    },
    // 计算点到线段的最短距离
    pointToSegmentDistance(x, y, p1, p2) {
      const x1 = p1[0], y1 = p1[1], x2 = p2[0], y2 = p2[1]
      const A = x - x1
      const B = y - y1
      const C = x2 - x1
      const D = y2 - y1
      const dot = A * C + B * D
      const len_sq = C * C + D * D
      let param = -1
      if (len_sq !== 0) param = dot / len_sq
      let xx, yy
      if (param < 0) {
        xx = x1
        yy = y1
      } else if (param > 1) {
        xx = x2
        yy = y2
      } else {
        xx = x1 + param * C
        yy = y1 + param * D
      }
      const dx = x - xx
      const dy = y - yy
      return Math.sqrt(dx * dx + dy * dy)
    },
  }
}
</script>

<style lang="less" scoped>
  .edit-tip, .select-tip {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style> 