<template></template>

<script setup lang="jsx">
  import { ref, reactive, onMounted, computed, watch, nextTick, useAttrs } from 'vue'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import { GeoJsonLayer } from '@deck.gl/layers'
  import { hexToRgb } from '@/utils/toDeckglRgb.js'
  // import { mapBoundGeo } from '@/utils/mapBounds.js'

  const props = defineProps(['geojson', 'mapIns', 'id'])
  const stateRef = ref({
    deckOverlay: null,
  })

  stateRef.value.deckOverlay = new MapboxOverlay({
    id: 'deck-geojson-point-layer-overlay',
    layers: [],
  })
  props.mapIns.addControl(stateRef.value.deckOverlay)

  const updateProps = () => {
    stateRef.value.deckOverlay.setProps({
      layers: [
        new GeoJsonLayer({
          id: 'geojson-layer-point-' + props.id,
          data: JSON.parse(JSON.stringify(props.geojson)),
          // pickable: true,

          // filled: true,
          // pointType: 'circle', // 'circle', 'square', 'triangle', 'star', 'cylinder', 'icon'
          // pointRadiusMinPixels: 5,
          // getRadius: 5,
          // // getRadius: f => f.properties.rainfall * 10,
          // getFillColor: d => {
          //   return [...hexToRgb('#E33B38'), 255]
          // },

          // stroked: true, // 是否描边
          // getLineColor: [255, 255, 255, 255], // 描边颜色
          // lineWidthMaxPixels: 1, // 周围最大线宽
          // lineWidthMinPixels: 1, // 周围最小线宽
          // // getText: d => d.properties.siteName + '\n降雨量: ' + d.properties.rainfall + ' mm',
          // // 处理 text
          // // getText: d => {
          // //   return `${d.properties.siteName}\n降雨量: ${d.properties.rainfall} mm`
          // // },
          // // getText: d => {
          // //   return d.properties?.siteName + '\n降雨量: ' + d.properties.rainfall + ' mm'
          // // },
          // getText: d => {
          //   return `1235`
          // },
          // getTextAnchor: 'start',
          // getTextPixelOffset: [11, -10],
          // getTextColor: [29, 33, 41, 255],
          // textCharacterSet: 'auto',
          // getTextSize: 12,
          // textOutlineColor: [255, 255, 255, 255],
          // textOutlineWidth: 7,
          // textFontSettings: { sdf: true, smoothing: 0.3 },

          // filled: true,
          pickable: true,
          pointType: 'circle+text',

          // 处理 circle 描边
          stroked: false, // 是否描边
          getLineColor: [255, 255, 255, 255], // 描边颜色
          lineWidthMaxPixels: 1, // 周围最大线宽
          lineWidthMinPixels: 1, // 周围最小线宽

          // 处理 circle 实心
          getFillColor: d => {
            return [...hexToRgb('#E33B38'), 255]
          },
          getPointRadius: d => {
            return 8
          },
          pointRadiusMinPixels: 6, // 最小半径
          pointRadiusMaxPixels: 25, // 最大半径

          // // 处理 text
          getText: d => {
            return `${d.properties.siteName}\n${d.properties.rainfall} mm`
          },
          getTextAnchor: 'start',
          getTextPixelOffset: [11, 1],
          getTextColor: d => {
            return [...hexToRgb('#E33B38'), 255] //#FFB400
          },
          textCharacterSet: 'auto',
          getTextSize: 13,
          getFontFamily: 'Arial Black', // 使用粗体字体
          getWeight: 900, // 设置字体粗细（可选）
          // textOutlineColor: [26, 23, 16, 255],
          // textOutlineColor: [255, 255, 255, 255],
          // textOutlineColor: [255, 195, 0, 255],
          textOutlineColor: [143, 36, 35, 255],
          textOutlineWidth: 1,
          textFontSettings: { sdf: true, smoothing: 0.3 },

          onClick: opt => {
            const options = JSON.parse(JSON.stringify(opt.object))
            
            // if (activeItem.value?.id === options.properties.id) {
            //   activeItem.value = null
            // } else {
            //   activeItem.value = options.properties
            // }
          },
        }),
      ],
    })
  }

  watch(
    () => props.geojson,
    newVal => {
      nextTick(() => {
        updateProps()
      })
    },
  )
</script>

<style scoped lang="less"></style>
