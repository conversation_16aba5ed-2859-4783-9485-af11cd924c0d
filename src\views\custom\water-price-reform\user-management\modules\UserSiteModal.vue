<template>
  <div>
    <ant-modal :visible="open" :modal-title="modalTitle" :loading="modalLoading" @cancel="cancel">
      <div slot="content">
        <div class="data-panel">
          <div class="table-panel">
            <VxeTable
              ref="vxeTableRef"
              :tableTitle="tableTitle"
              :loading="loading"
              :columns="columns"
              :tableData="dataSource"
              :tablePage="false"
              :isShowTableHeader="false"
              :isShowSetBtn="false"
            />
          </div>
          <div class="add-site-btn-bar">
            <a-button type="dashed" icon="plus" @click="addSite" style="width: 100%">添加站点</a-button>
          </div>
        </div>
      </div>
      <template slot="footer">
        <a-button @click="cancel">取消</a-button>
        <a-button type="primary" @click="submitForm" :loading="loading">确定</a-button>
      </template>
    </ant-modal>
  </div>
</template>
<script lang="jsx">
  import AntModal from '@/components/pt/dialog/AntModal'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import { getDeviceCategoryTree, getProjectCategoryTree } from '@/api/common'
  import draggable from 'vuedraggable'
  import cloneDeep from 'lodash.clonedeep'
  import * as _ from 'lodash'
  import difference from 'lodash/difference'
  import getFlatTreeMap from '@/utils/getMapFlatTree'
  import TreeGeneral from '@/components/TreeGeneral'
  import VxeTable from '@/components/VxeTable'
  import VxeTableForm from '@/components/VxeTableForm'
  import { addWaterSite, getWaterSiteList, getObjectCategoryCodeSites } from '../services'

  export default {
    name: 'UserSiteModal',
    props: {},
    components: {
      TreeGeneral,
      VxeTable,
      VxeTableForm,
      AntModal,
      AdvanceTable,
      draggable,
    },
    data() {
      return {
        rawData: [],
        useWaterId: null,
        loading: false,
        modalLoading: false,
        dataSource: [], //objData,
        open: false,
        modalTitle: '',
        tableTitle: '',
        list: [],
        columns: [
          { type: 'seq', title: '序号', width: 60 },
          {
            title: '站点编码',
            field: 'siteCode',
            slots: {
              default: ({ row, rowIndex }) => {
                if (row.edit) {
                  return (
                    <a-select
                      v-model={row.siteCode}
                      placeholder='请选择站点'
                      style='width: 100%'
                      onChange={() => this.onSiteCodeChange(row, rowIndex)}
                    >
                      {this.availableSiteOptions(rowIndex).map(item => (
                        <a-select-option value={item.siteCode}>{item.siteName}</a-select-option>
                      ))}
                    </a-select>
                  )
                } else {
                  return <span>{row.siteCode}</span>
                }
              },
            },
          },
          {
            title: '站点名称',
            field: 'siteName',
          },
          {
            title: '操作',
            field: 'action',
            width: 60,
            slots: {
              default: ({ row, rowIndex }) => {
                return (
                  <span>
                    <a onClick={() => this.removeSite(rowIndex)}>删除</a>
                  </span>
                )
              },
            },
          },
        ],

        siteOptions: [],
      }
    },
    computed: {},
    watch: {},
    created() {
      this.init()
    },
    mounted() {},
    methods: {
      init() {
        getObjectCategoryCodeSites().then(res => {
          this.rawData = res.data
          this.siteOptions = res.data
        })
      },
      addSite() {
        // 检查是否还有可选站点
        const selectedCodes = this.dataSource.map(item => item.siteCode)
        const available = this.siteOptions.filter(item => !selectedCodes.includes(item.siteCode))
        if (available.length === 0) {
          this.$message.warning('没有可添加的站点了')
          return
        }
        this.dataSource.push({ siteCode: '', siteName: '', edit: true })
      },
      removeSite(index) {
        this.dataSource.splice(index, 1)
      },
      onSiteCodeChange(row, rowIndex) {
        const selected = this.siteOptions.find(item => item.siteCode === row.siteCode)
        if (selected) {
          row.siteCode = selected.siteCode
          row.siteName = selected.siteName
          row.edit = false
        }
      },
      availableSiteOptions(index) {
        const selectedCodes = this.dataSource.filter((item, i) => i !== index).map(item => item.siteCode)
        return this.siteOptions.filter(item => !selectedCodes.includes(item.siteCode))
      },
      getList() {
        this.loading = true
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      /** 提交按钮 */
      submitForm() {
        if (this.dataSource.length === 0 || this.dataSource.some(item => !item.siteCode)) {
          this.$message.error('请为每一行选择站点！')
          return
        }
        this.loading = true

        let param = this.dataSource.map(item => {
          const matched = this.rawData.find(d => d.siteCode === item.siteCode || d.siteName === item.siteName)
          return {
            siteId: matched ? matched.siteId : null,
            useWaterId: this.useWaterId,
          }
        })

        addWaterSite(param).then(res => {
          this.$message.success('配置成功', 3)
          this.open = false
          this.loading = false
          this.$emit('ok')
        })
        // setTimeout(() => {
        //   this.$message.success('配置成功', 3)
        //   this.open = false
        //   this.loading = false
        //   this.$emit('ok')
        // }, 600)
      },
      /** 修改按钮操作 */
      handleUpdate(record) {
        this.open = true
        this.modalTitle = `${record.useWaterName}水量计量站点配置`
        this.useWaterId = record.useWaterId
        getWaterSiteList({ useWaterId: record.useWaterId }).then(response => {
          this.dataSource = response.data
        })
      },
    },
  }
</script>
<style lang="less" scoped>
  @import '~ant-design-vue/lib/style/index';

  ::v-deep .data-panel {
    display: flex;
    flex-direction: column;
    background-color: #f0f2f5;

    // background-color: blue;
    height: 460px;
    // height: 820px;
    width: 100%;

    .table-panel {
      flex: 1;
      background-color: #f0f2f5;
    }
  }

  // ::v-deep .ant-input-affix-wrapper .ant-input {
  //   margin-top: -15px !important;
  //   margin-left: 10px !important;
  //   width: 200px !important;
  //   position: relative;
  //   text-align: inherit;
  // }

  ::v-deep .ant-modal-body {
    width: 100% !important;
    height: 495px !important;
    max-height: 495px !important;
    overflow: hidden !important;
  }

  ::v-deep .ant-table-wrapper {
    height: 300px !important;

    overflow-y: auto;
    overflow-x: hidden;
    background-color: slateblue;
  }

  ::v-deep .modal-content {
    height: 495px !important;
    width: 100% !important;
    // background-color: brown;
  }

  ::v-deep .ant-modal-content {
    height: 620px;
    width: 100%;
    // background-color: yellow;
    overflow-x: hidden;
  }

  .add-site-btn-bar {
    width: 100%;
  }
</style>
